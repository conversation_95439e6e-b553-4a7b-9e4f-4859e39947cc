import React, { useEffect, useRef } from 'react';
import _size from 'lodash/size';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import _includes from 'lodash/includes';
import _map from 'lodash/map';
import { Redirect } from 'expo-router';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import userReader from 'core/readers/userReader';
import Loading from 'atoms/Loading';
import GameFullPage from 'modules/game/pages/GameFullPage';
import PlayGamePageWrapper from 'modules/game/components/PlayGamePageWrapper';
import useJoinGameQuery from '../../hooks/useJoinGameQuery';
import { GAME_STATUS } from '../../constants/game';
import useGameOwner from '../../hooks/useGameOwner';
import WaitingForFriend from '../../pages/PlayWithFriend/components/WaitingForFriend';
import useGameContext from '../../hooks/useGameContext';

const JOINED_GAME_CALLED: any = {};

const PlayGameMainLayoutWrapper = ({
  COMPONENT_FACTORY,
}: {
  COMPONENT_FACTORY: any;
}) => {
  const { game } = useGameContext();
  const { user } = useSession();
  const { joinGame } = useJoinGameQuery();

  const { players, gameStatus, _id: gameId, config } = game;

  const { checkIsGameOwner } = useGameOwner();
  const isGameOwner = checkIsGameOwner({ game });

  const isUserInTheGame = _includes(
    _map(players, 'userId'),
    userReader.id(user),
  );

  console.info('RITESH : PLAY GAME MAIN LAYOUT WRAPPER', game);

  const { numPlayers } = config;

  const joinGameRef = useRef(joinGame);
  joinGameRef.current = joinGame;

  useEffect(() => {
    if (JOINED_GAME_CALLED[gameId]) return;

    JOINED_GAME_CALLED[gameId] = true;

    const shouldJoinTheGame =
      gameStatus === GAME_STATUS.CREATED &&
      _size(players) < numPlayers &&
      !isGameOwner;
    if (shouldJoinTheGame) {
      joinGameRef.current({ gameId }).then(() => {
        Analytics.track(ANALYTICS_EVENTS.JOIN_GAME, { gameId });
      });
    }
  }, [gameStatus, players, isGameOwner, gameId]);

  if (_size(players) === numPlayers && !isUserInTheGame) {
    return <GameFullPage />;
  }

  if (gameStatus === GAME_STATUS.ENDED) {
    return <Redirect href={`/game/${gameId}/result`} />;
  }

  if (gameStatus === GAME_STATUS.CREATED && _size(players) === 1) {
    if (isGameOwner) {
      return <WaitingForFriend game={game} />;
    }
    return <Loading label="Joining the Game..." />;
  }

  const Component = COMPONENT_FACTORY?.[gameStatus];

  if (!Component) {
    return null;
  }

  return <Component game={game} />;
};

const PlayGameMainLayout = ({
  COMPONENT_FACTORY,
}: {
  COMPONENT_FACTORY: any;
}) => (
  <PlayGamePageWrapper>
    <PlayGameMainLayoutWrapper COMPONENT_FACTORY={COMPONENT_FACTORY} />
  </PlayGamePageWrapper>
);

export default React.memo(PlayGameMainLayout);
