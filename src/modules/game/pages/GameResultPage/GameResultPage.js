import React from 'react';
import _toNumber from 'lodash/toNumber';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import _isEmpty from 'lodash/isEmpty';
import GroupPlayResult from 'modules/game/pages/GroupPlayResult';
import { GAME_TYPES } from 'core/constants/gameTypes';
import useGameContext from '../../hooks/useGameContext';
import Loading from '../../../../components/atoms/Loading';
import { GAME_STATUS } from '../../constants/game';
import GameErrorPage from '../GameErrorPage';
import GameAnalysis from '../GameAnalysis';
import GameResultContainer from './GameResult';
import gameReader from '@/src/core/readers/gameReader';
import { EMPTY_OBJECT } from '@/src/core/constants/general';

const GameResultPage = (props) => {
  const gameContextValue = useGameContext();
  const { game, gameMeta, players } = gameContextValue ?? EMPTY_OBJECT;
  const { loading, error } = gameMeta ?? EMPTY_OBJECT;
  const {
    _id: gameId,
    startTime,
    config,
    gameStatus,
    gameType,
  } = game ?? EMPTY_OBJECT;

  const startTimeDate = new Date(startTime);
  const currentTime = getCurrentTimeWithOffset();

  const timeLimit = gameReader.timeLimit(game);
  const endTime = startTimeDate.getTime() + _toNumber(timeLimit) * 1000;

  const hasGameTimeLimitOver = endTime <= currentTime;

  const isEndingGame =
    hasGameTimeLimitOver && gameStatus === GAME_STATUS.STARTED;

  if (gameStatus === GAME_STATUS.ENDED && gameType === GAME_TYPES.GROUP_PLAY) {
    return <GroupPlayResult />;
  }

  if (
    (gameStatus === GAME_STATUS.ENDED && gameType === GAME_TYPES.FLASH_ANZAN) ||
    gameType === GAME_TYPES.FASTEST_FINGER
  ) {
    return <GameAnalysis gameId={gameId} />;
  }

  if (gameStatus === GAME_STATUS.ENDED) {
    return <GameResultContainer />;
  }

  if (isEndingGame || loading || _isEmpty(game)) {
    return <Loading label="Calculating result..." />;
  }

  return <GameErrorPage />;
};

export default React.memo(GameResultPage);
