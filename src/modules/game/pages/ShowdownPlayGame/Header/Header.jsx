import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { View } from 'react-native';
import { Text } from '@rneui/themed';

import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import _toNumber from 'lodash/toNumber';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import _isArray from 'lodash/isArray';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import _isNaN from 'lodash/isNaN';
import useGameContext from '../../../hooks/useGameContext';
import getCurrentTimeWithOffset from '../../../../../core/utils/getCurrentTimeWithOffset';
import CardDuringMobileGame from '../../../../showdown/components/usercard/CardDuringMobileGame';
import CardDuringGame from '../../../../showdown/components/usercard/CardDuringGame';
import { useSession } from '../../../../auth/containers/AuthProvider';
import styles from './Header.style';
import gameReader from '@/src/core/readers/gameReader';
import { EMPTY_OBJECT } from '@/src/core/constants/general';

const Header = ({
  playersScores,
  onGameEnded,
  showdownGamePlayers,
  isReady,
}) => {
  const { players, game } = useGameContext();
  const { config = EMPTY_OBJECT, startTime } = game;
  const { user: currentUser } = useSession();

  const currentPlayer = useMemo(
    () => players.find((player) => player._id === currentUser?._id),
    [players, currentUser],
  );

  const opponent = useMemo(
    () => players.find((player) => player._id !== currentUser?._id),
    [players, currentUser],
  );

  const { isMobile } = useMediaQuery();
  const timeLimit = gameReader.timeLimit(game);

  const startTimeDate = new Date(startTime);
  const currentTime = getCurrentTimeWithOffset();

  const endTime = startTimeDate.getTime() + _toNumber(timeLimit) * 1000;
  const timeLeftInSecond = Math.max(
    Math.ceil((endTime - currentTime) / 1000),
    0,
  );

  const [timer, setTimer] = useState(timeLeftInSecond);

  const formatTime = useCallback((seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes} : ${remainingSeconds < 10 ? '0' : ''}${Math.floor(remainingSeconds)}`;
  }, []);

  const currTimeRef = useRef();

  const userPoints = useMemo(() => {
    if (
      _isNil(showdownGamePlayers) &&
      _isArray(showdownGamePlayers) &&
      showdownGamePlayers.length == 2
    ) {
      return '0 - 0';
    }
    let currentUserScore = 0;
    let opponentUserScore = 0;
    showdownGamePlayers.forEach((item) => {
      if (item?.userId === currentUser?._id) {
        currentUserScore = item?.wins ?? 0;
      } else {
        opponentUserScore = item?.wins ?? 0;
      }
    });
    return `${currentUserScore} - ${opponentUserScore}`;
  }, [showdownGamePlayers]);

  const getUserScore = useCallback(
    ({ user }) => {
      if (_isEmpty(user)) return 0;
      const { _id: userId } = user;
      return playersScores[userId];
    },
    [playersScores],
  );

  const renderPlayerDesktop = useCallback(
    ({ user }) => {
      const score = getUserScore({ user });
      return <CardDuringGame user={user} score={score} />;
    },
    [getUserScore],
  );
  const renderPlayerMobile = useCallback(
    ({ user }) => {
      const score = getUserScore({ user });
      return <CardDuringMobileGame user={user} score={score} />;
    },
    [getUserScore],
  );

  useEffect(() => {
    if (currTimeRef.current) {
      clearInterval(currTimeRef.current);
    }

    currTimeRef.current = setInterval(() => {
      const timeLeft = (endTime - getCurrentTimeWithOffset()) / 1000;
      if (timeLeft <= 0) {
        onGameEnded();
        clearInterval(currTimeRef.current);
        return;
      }
      setTimer(Math.max(timeLeft, 0));
    }, 1000);

    return () => clearInterval(currTimeRef.current);
  }, [timer, endTime, onGameEnded]);

  useEffect(() => {
    if (timer <= 0) {
      onGameEnded();
    }
  }, [timer, onGameEnded]);

  if (isMobile) {
    return (
      <View style={[styles.container, styles.containerMob]}>
        {renderPlayerMobile({ user: currentPlayer })}
        <View style={styles.timerContainer}>
          <Text style={styles.userPoints}>{userPoints}</Text>
          {!_isNaN(timer) && isReady ? (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'flex-end',
              }}
            >
              <MaterialCommunityIcons
                name="timer-outline"
                color="white"
                size={16}
              />
              <Text style={styles.timerText2}> {formatTime(timer)}</Text>
            </View>
          ) : null}
        </View>
        {renderPlayerMobile({ user: opponent })}
      </View>
    );
  }
  return (
    <View style={styles.container}>
      {renderPlayerDesktop({ user: currentPlayer })}
      <View style={styles.timerContainer}>
        <Text style={styles.userPoints}>{userPoints}</Text>
        {!_isNaN(timer) && isReady ? (
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'flex-end',
            }}
          >
            <MaterialCommunityIcons
              name="timer-outline"
              color="white"
              size={16}
            />
            <Text style={styles.timerText2}> {formatTime(timer)}</Text>
          </View>
        ) : null}
      </View>
      {renderPlayerDesktop({ user: opponent })}
    </View>
  );
};

export default React.memo(Header);
