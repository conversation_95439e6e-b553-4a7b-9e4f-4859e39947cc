import React, { useCallback, useMemo } from 'react'
import { Image, View } from 'react-native'
import { Text } from '@rneui/themed'
import useGameContext from '../../../../hooks/useGameContext'
import GameHeaderPlayerCardWeb from '@/src/modules/game/components/usercard/GameHeaderPlayerCardWeb'
import GameHeaderPlayerCard from '@/src/modules/game/components/usercard/GameHeaderPlayerCard'
import useMediaQuery from '@/src/core/hooks/useMediaQuery'
import _toNumber from 'lodash/toNumber'
import _isEmpty from 'lodash/isEmpty'
import cardBackground from '@/assets/images/gameMobBack.png'
import MaterialIcons from '@expo/vector-icons/MaterialIcons'
import dark from 'core/constants/themes/dark'
import { getFormattedTime } from '../../../../../../core/utils/general'
import styles from './GameAnalysisHeader.style'
import _reduce from "lodash/reduce"
import _map from "lodash/map"
import _isNil from "lodash/isNil"
import gameReader from '@/src/core/readers/gameReader'
import { EMPTY_OBJECT } from '@/src/core/constants/general'

const GameAnalysisHeader = (props) => {
    const { players, game } = useGameContext()

    const playersScores = useMemo(() => {
        const scores = _reduce(game?.leaderBoard, (result, item) => {
            if (!_isNil(item?.userId) && !_isNil(item?.correct)) {
                result[item.userId] = item.correct;
            }
            return result;
        }, {});
        return scores
    }, [game])

    const { config = EMPTY_OBJECT, startTime, gameType } = game

    const { isMobile } = useMediaQuery()

    const timeLimit = gameReader.timeLimit(game)

    const getUserScore = useCallback(
        ({ user }) => {
            if (_isEmpty(user)) return 0
            const { _id: userId } = user
            return playersScores[userId]
        },
        [playersScores]
    )

    const renderPlayer = useCallback(
        ({ user }) => {
            const score = getUserScore({ user })
            return <GameHeaderPlayerCardWeb user={user} score={score} gameType={gameType}/>
        },
        [getUserScore]
    )
    const renderPlayer2 = useCallback(
        ({ user }) => {
            const score = getUserScore({ user })
            return <GameHeaderPlayerCard user={user} score={score} />
        },
        [getUserScore]
    )


    if (isMobile) {
        return (
            <View style={[styles.container, styles.containerMob]}>
                <Image source={cardBackground} style={styles.backImg} />
                {renderPlayer2({ user: players[0] })}
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'flex-end',
                    }}
                >
                    <MaterialIcons name={'timer'} color={'white'} size={16} />
                    <Text style={styles.timerText2}> {getFormattedTime(timeLimit)}</Text>
                </View>

                {renderPlayer2({ user: players[1] })}
            </View>
        )
    } else
        return (
            <View style={styles.container}>
                {renderPlayer({ user: players[0] })}
                <View style={styles.timerBox}>
                    <MaterialIcons name={'timer'} color={dark.colors.textDark} size={20} />
                    <Text style={styles.timerText}> {getFormattedTime(timeLimit)}</Text>
                </View>
                {renderPlayer({ user: players[1] })}
            </View>
        )
}

GameAnalysisHeader.propTypes = {
}

export default GameAnalysisHeader
