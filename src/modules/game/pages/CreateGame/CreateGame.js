import React, { useCallback, useMemo, useState } from 'react';
import { Dimensions, Text, TouchableOpacity, View } from 'react-native';
import Header from 'shared/Header';
import PrimaryButton from 'atoms/PrimaryButton';
import _isEmpty from 'lodash/isEmpty';
import MultiselectDropdown from 'shared/MultiselectDropdown';
import { FORM_INPUT_TYPES } from 'core/constants/forms';
import { router } from 'expo-router';
import RangeSelector from 'shared/RangeSelector';
import dark from 'core/constants/themes/dark';
import useMediaQuery from 'core/hooks/useMediaQuery';
import useGoBack from 'navigator/hooks/useGoBack';
import { Icon } from '@rneui/base';
import { GAME_TYPES } from 'modules/home/<USER>/gameTypes';
import useCreateNewGameQuery from '../../../home/<USER>/useCreateNewGameQuery';
import NumberInput from '../../../../components/shared/Form/components/NumberInput/NumberInput';
import styles from './CreateGame.style';
import {
  PRE_SELECTED_CATEGORIES,
  QUESTION_CATEGORIES,
} from '../../constants/questionCategories';

import { EACH_QUESTION_RESULT_TIME } from '../../constants/game';

const CreateGame = () => {
  const { createGame, creatingGame } = useCreateNewGameQuery();
  const [selectedQueCategories, setSelectedQueCategories] =
    useState(EMPTY_ARRAY);
  const [noOfQuestions, setNoOfQuestions] = useState(12);
  const [maxTimePerQuestion, setMaxTimePerQuestion] = useState(10);
  const [difficultyMinRating, setDifficultyMinRating] = useState(1000);
  const [difficultyMaxRating, setDifficultyMaxRating] = useState(1500);

  const gameTimeInSeconds = useMemo(
    () => (maxTimePerQuestion + EACH_QUESTION_RESULT_TIME) * noOfQuestions,
    [maxTimePerQuestion, noOfQuestions],
  );

  const { goBack } = useGoBack();

  const createGameWithSelectedConfig = useCallback(() => {
    if (creatingGame) {
      return;
    }

    createGame({
      numPlayers: 50,
      timeLimit: gameTimeInSeconds,
      gameType: GAME_TYPES.GROUP_PLAY,
      questionTags: selectedQueCategories,
      difficultyLevel: [difficultyMinRating, difficultyMaxRating],
      maxTimePerQuestion,
    }).then((createdGame) => {
      if (_isEmpty(createdGame)) return null;
      const { _id } = createdGame;
      router.push(`/game/${_id}/play`);
    });
  }, [
    selectedQueCategories,
    gameTimeInSeconds,
    creatingGame,
    difficultyMinRating,
    difficultyMaxRating,
    maxTimePerQuestion,
  ]);

  const { isMobile: isCompactMode } = useMediaQuery();

  const handleOnDifficultyChange = useCallback(({ from, to }) => {
    setDifficultyMaxRating(to);
    setDifficultyMinRating(from);
  }, []);

  return (
    <View
      style={[
        styles.container,
        !isCompactMode && { justifyContent: 'center', alignItems: 'center' },
      ]}
    >
      <Header title="Create Game" />

      <View
        style={[
          styles.innerContainer,
          !isCompactMode && {
            width: Dimensions.get('window').width * 0.4,
            paddingHorizontal: 30,
            borderLeftColor: dark.colors.tertiary,
            borderRightColor: dark.colors.tertiary,
            borderLeftWidth: 1,
            borderRightWidth: 1,
          },
        ]}
      >
        <View style={styles.inputs}>
          {!isCompactMode && (
            <View
              style={{
                ...styles.header,
                backgroundColor: 'transparent',
              }}
            >
              <View style={styles.headerLeft}>
                <TouchableOpacity
                  onPress={goBack}
                  hitSlop={{ left: 12, right: 12 }}
                  style={{ width: 20 }}
                >
                  <Icon
                    name="chevron-left"
                    type="font-awesome-5"
                    color="white"
                    size={20}
                  />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>Create Game</Text>
              </View>
            </View>
          )}
          <Text style={styles.labelText}>Question Category</Text>
          <MultiselectDropdown
            key="category-selection"
            data={QUESTION_CATEGORIES}
            handleOnChange={setSelectedQueCategories}
            placeholderText="Select Question Category"
            initialSelectedItem={PRE_SELECTED_CATEGORIES}
          />

          <View style={styles.divider} />
          <NumberInput
            field={{
              label: 'No. of Questions',
              type: FORM_INPUT_TYPES.NUMBER_INPUT,
              rules: {
                minValue: 5,
                maxValue: 600,
              },
              additional: {
                incrementBy: 1,
              },
            }}
            value={noOfQuestions}
            onValueChange={setNoOfQuestions}
          />
          <View style={[styles.divider, { marginTop: 5 }]} />
          <NumberInput
            field={{
              label: 'Time Per Question (in sec)',
              type: FORM_INPUT_TYPES.NUMBER_INPUT,
              rules: {
                minValue: 5,
                maxValue: 60,
              },
              additional: {
                incrementBy: 1,
              },
            }}
            value={maxTimePerQuestion}
            onValueChange={setMaxTimePerQuestion}
          />
          {/* <Text style={[styles.labelText, {color: dark.colors.textDark, fontFamily: "Montserrat-600"}]}> */}
          {/*  Total Game Time : */}
          {/*  <Text style={styles.labelText}>{` ${_round(gameTimeInSeconds / 60, 1)} `}Min</Text></Text> */}
          <View style={[styles.divider, { marginTop: 5 }]} />
          <Text
            style={styles.labelText}
          >{`Difficulty Range [ ${difficultyMinRating} - ${difficultyMaxRating} ]`}</Text>
          <RangeSelector
            key="difficulty-range"
            initialFromValue={difficultyMinRating}
            initialToValue={difficultyMaxRating}
            onValueChange={handleOnDifficultyChange}
            step={100}
          />
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              // paddingHorizontal: 20,
              paddingBottom: 8,
            }}
          >
            <Text style={styles.labelText}>500</Text>
            <Text style={styles.labelText}>1000</Text>
            <Text style={styles.labelText}>1500</Text>
            <Text style={styles.labelText}>2000</Text>
            <Text style={styles.labelText}>2500</Text>
            <Text style={styles.labelText}>3000</Text>
          </View>
        </View>
        <View
          style={[
            !isCompactMode && { alignItems: 'flex-end' },
            { width: '100%' },
          ]}
        >
          <PrimaryButton
            onPress={createGameWithSelectedConfig}
            label={creatingGame ? 'Creating Link...' : 'Create Link'}
            radius={20}
            buttonStyle={{
              height: 40,
              width: isCompactMode ? Dimensions.get('window').width - 32 : 190,
            }}
          />
        </View>
      </View>
    </View>
  );
};

export default React.memo(CreateGame);
