import React, { useCallback, useEffect, useRef, useState } from 'react'
import { Animated, StyleSheet, View } from 'react-native'
import { Text } from '@rneui/themed'
import GameHeaderPlayerCard from '@/src/modules/game/components/usercard/GameHeaderPlayerCard'
import useMediaQuery from '@/src/core/hooks/useMediaQuery'
import _toNumber from 'lodash/toNumber'
import _isEmpty from 'lodash/isEmpty'
import MaterialIcons from '@expo/vector-icons/MaterialIcons'
import useGameContext from '../../../hooks/useGameContext'
import dark from '../../../../../core/constants/themes/dark'
import getCurrentTimeWithOffset from '../../../../../core/utils/getCurrentTimeWithOffset'
import gameReader from '@/src/core/readers/gameReader'
import { EMPTY_OBJECT } from '@/src/core/constants/general'

const styles = StyleSheet.create({
  container: {
    maxWidth: 420,
    width: '98%',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    height: 80,
    gap: 0,
  },
  containerMob: {
    width: '90%',
    overflow: 'hidden',
  },
  containerWeb: {
    width: '100%',
    overflow: 'hidden',
  },
  timerBox: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    width: 90,
    justifyContent: 'center',
  },
  timerText: {
    marginBottom: -1,
    fontSize: 14,
    fontFamily: 'Montserrat-600',
    color: dark.colors.timerColor,
  },
  timerText2: {
    marginBottom: -1,
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: dark.colors.timerColor,
  },
  backImg: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    resizeMode: 'stretch',
  },
  timerMainContainer: {
    width: 80,
    height: 30,
    marginTop: 52,
    flexDirection: 'row',
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  timerContainer: {
    width: 56,
    height: 28,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    borderWidth: 0.5,
    borderColor: dark.colors.tertiary,
    borderRadius: 10,
  },
  emptyIcon: {
    width: 10,
    height: 10,
  },
});

const Header = ({ playersScores, onGameEnded }) => {
  const { players, game } = useGameContext();
  const { config = EMPTY_OBJECT, startTime, gameType } = game

  const { isMobile } = useMediaQuery();

  const initialTranslateX = 50;
  const player1TranslateX = useRef(
    new Animated.Value(initialTranslateX),
  ).current;
  const player2TranslateX = useRef(
    new Animated.Value(-initialTranslateX),
  ).current;

  const timeLimit = gameReader.timeLimit(game);
  const startTimeDate = new Date(startTime);
  const currentTime = getCurrentTimeWithOffset();

  const endTime = startTimeDate.getTime() + _toNumber(timeLimit) * 1000;
  const timeLeftInSecond = Math.max(
    Math.ceil((endTime - currentTime) / 1000),
    0,
  );

  const [timer, setTimer] = useState(timeLeftInSecond);

  const formatTime = useCallback((seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${Math.floor(remainingSeconds)}`;
  }, []);

  const currTimeRef = useRef();

  const getUserScore = useCallback(
    ({ user }) => {
      if (_isEmpty(user)) return 0
      const { _id: userId } = user
      return playersScores[userId]
    },
    [playersScores],
  );

  useEffect(() => {
    const playersLoaded = !_isEmpty(players?.[0]) && !_isEmpty(players?.[1]);
    if (playersLoaded) {
      Animated.parallel([
        Animated.timing(player1TranslateX, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(player2TranslateX, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [players, player1TranslateX, player2TranslateX]);

  const renderPlayer = useCallback(({user, translateX}) => {
    const score = getUserScore({user})
    return(
      <Animated.View style={{ transform: [{ translateX: translateX }] }}>
        <GameHeaderPlayerCard
          user={user}
          score={score}
          gameType={gameType}
        />
      </Animated.View>
    )
  }, [getUserScore])

  useEffect(() => {
    if (currTimeRef.current) {
      clearInterval(currTimeRef.current);
    }

    currTimeRef.current = setInterval(() => {
      const timeLeft = (endTime - getCurrentTimeWithOffset()) / 1000
      if (timeLeft <= 0) {
        onGameEnded();
        clearInterval(currTimeRef.current)
        return;
      }
      setTimer(Math.max(timeLeft, 0))
    }, 1000);

    return () => clearInterval(currTimeRef.current)
  }, [timer, endTime, onGameEnded])

  useEffect(() => {
    if (timer <= 0) {
      onGameEnded()
    }
  }, [timer, onGameEnded])

  const player0Score = getUserScore({ user: players[0] })
  const player1Score = getUserScore({ user: players[1] })

  return (
    <View
      style={[
        styles.container,
        isMobile ? styles.containerMob : styles.containerWeb,
      ]}
    >
      {renderPlayer({user: players?.[0],translateX: player1TranslateX})}
      <View style={styles.timerMainContainer}>
        {player0Score > player1Score ? (
          <MaterialIcons
            name="arrow-back-ios"
            color={dark.colors.headerUserBorderColor}
            size={10}
          />
        ) : (
          <View styles={styles.emptyIcon}></View>
        )}
        <View style={[styles.timerContainer]}>
          <MaterialIcons
            name="timer"
            color={dark.colors.timerColor}
            size={10}
          />
          <Text style={styles.timerText2}> {formatTime(timer)}</Text>
        </View>
        {player1Score > player0Score ? (
          <MaterialIcons
            name="arrow-forward-ios"
            color={dark.colors.headerOpponentBorderColor}
            size={10}
          />
        ) : (
          <View styles={styles.emptyIcon}></View>
        )}
      </View>
      {renderPlayer({user: players?.[1],translateX: player2TranslateX})}
    </View>
  )
}

export default Header
