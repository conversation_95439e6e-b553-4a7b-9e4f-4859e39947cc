import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Image, View } from 'react-native'
import { Text } from '@rneui/themed'
import useGameContext from '../../../../hooks/useGameContext'
import GameHeaderPlayerCardWeb from '@/src/modules/game/components/usercard/GameHeaderPlayerCardWeb'
import GameHeaderPlayerCard from '@/src/modules/game/components/usercard/GameHeaderPlayerCard'
import useMediaQuery from '@/src/core/hooks/useMediaQuery'
import cardBackground from '@/assets/images/gameMobBack.png'
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset'
import styles from './FlashAnzanScoreHeader.style'
import _map from 'lodash/map';
import _keyBy from 'lodash/keyBy';
import _get from 'lodash/get';
import _reduce from "lodash/reduce"
import _toNumber from 'lodash/toNumber'
import _isEmpty from 'lodash/isEmpty'
import PropTypes from 'prop-types'
import { GAME_TYPES } from '../../../../../home/<USER>/gameTypes'
import gameReader from '@/src/core/readers/gameReader'
import { EMPTY_OBJECT } from '@/src/core/constants/general'

const getPlayerScores = ({ game }) => {
    const { leaderBoard, players } = game;
    const leaderBoardByUserId = _keyBy(leaderBoard, entry => _get(entry, 'userId'));
    const scores = _map(players, player => ({
        userId: player?.userId,
        score: _get(leaderBoardByUserId[player?.userId], 'totalPoints', 0),
    }));
    const playersScore = _reduce(scores, (acc, { userId, score }) => {
        acc[userId] = score;
        return acc;
    }, {});
    return playersScore
};

const FlashAnzanScoreHeader = (props) => {
    const { currentQuestion } = props

    const { players, game } = useGameContext()

    const playersScores = useMemo(() => getPlayerScores({ game }), [game])

    const { config = EMPTY_OBJECT, startTime } = game

    const { isMobile } = useMediaQuery()

    const timeLimit = gameReader.timeLimit(game)

    const startTimeDate = new Date(startTime)
    const currentTime = getCurrentTimeWithOffset()

    const endTime = startTimeDate.getTime() + _toNumber(timeLimit) * 1000
    const timeLeftInSecond = Math.max(Math.ceil((endTime - currentTime) / 1000), 0)

    const [timer, setTimer] = useState(timeLeftInSecond)

    const formatTime = useCallback((seconds) => {
        const minutes = Math.floor(seconds / 60)
        const remainingSeconds = seconds % 60
        return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${Math.floor(remainingSeconds)}`
    }, [])

    const currTimeRef = useRef()

    const getUserScore = useCallback(
        ({ user }) => {
            if (_isEmpty(user)) return 0
            const { _id: userId } = user
            return playersScores[userId]
        },
        [playersScores]
    )
    const renderPlayer2 = useCallback(
        ({ user }) => {
            const score = getUserScore({ user })
            return <GameHeaderPlayerCard user={user} score={score} gameType={GAME_TYPES.FLASH_ANZAN} />
        },
        [getUserScore]
    )

    useEffect(() => {
        if (currTimeRef.current) {
            clearInterval(currTimeRef.current)
        }

        currTimeRef.current = setInterval(() => {
            const timeLeft = (endTime - getCurrentTimeWithOffset()) / 1000;
            if (timeLeft <= 0) {
                clearInterval(currTimeRef.current);
                return;
            }
            setTimer(Math.max(timeLeft, 0));
        }, 1000)

        return () => clearInterval(currTimeRef.current)
    }, [timer, endTime,])
    return (
        <View style={[styles.container, styles.containerMob]}>
            {renderPlayer2({ user: players[0] })}
            <View style={{ flexDirection: 'row', alignItems: 'flex-end' }}>
                <Text style={styles.timerText2}> {`${currentQuestion}/3`}</Text>
            </View>
            {renderPlayer2({ user: players[1] })}
        </View>
    )
}

FlashAnzanScoreHeader.propTypes = {
    currentQuestion: PropTypes.number
}

export default FlashAnzanScoreHeader
