import React, { useCallback, useEffect, useRef, useState } from 'react';
import { View } from 'react-native';
import { Text } from '@rneui/themed';
import GameHeaderPlayerCard from '@/src/modules/puzzleGame/components/usercard/GameHeaderPlayerCard';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import _toNumber from 'lodash/toNumber';
import _isEmpty from 'lodash/isEmpty';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import dark from 'core/constants/themes/dark';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import usePuzzleGameContext from '../../hooks/usePuzzleGameContext';
import styles from './CrossMathPuzzleGameHeader.style';
import puzzleGameReader from '@/src/core/readers/puzzleGameReader';
import { EMPTY_OBJECT } from '@/src/core/constants/general';

const CrossMathPuzzleGameHeader = ({
  playersScores,
}: {
  playersScores: any;
}) => {
  const { players, game } = usePuzzleGameContext() ?? EMPTY_OBJECT;
  const { config = EMPTY_OBJECT, startTime, gameType } = game;

  const { isMobile } = useMediaQuery();

  const timeLimit = puzzleGameReader.timeLimit(game);
  const startTimeDate = new Date(startTime);
  const currentTime = getCurrentTimeWithOffset();

  const endTime = startTimeDate.getTime() + _toNumber(timeLimit) * 1000;
  const timeLeftInSecond = Math.max(
    Math.ceil((endTime - currentTime) / 1000),
    0,
  );

  const [timer, setTimer] = useState(timeLeftInSecond);

  const formatTime = useCallback((seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${Math.floor(remainingSeconds)}`;
  }, []);

  const currTimeRef = useRef();

  const getUserScore = useCallback(
    ({ user }: { user: any }) => {
      if (_isEmpty(user)) return 0;
      const { _id: userId } = user;
      return playersScores[userId];
    },
    [playersScores],
  );

  const renderPlayer2 = useCallback(
    ({ user }: { user: any }) => {
      const score = getUserScore({ user });
      return (
        <GameHeaderPlayerCard user={user} score={score} gameType={gameType} />
      );
    },
    [getUserScore],
  );

  useEffect(() => {
    if (currTimeRef.current) {
      clearInterval(currTimeRef.current);
    }

    currTimeRef.current = setInterval(() => {
      const timeLeft = (endTime - getCurrentTimeWithOffset()) / 1000;
      if (timeLeft <= 0) {
        clearInterval(currTimeRef.current);
        return;
      }
      setTimer(Math.max(timeLeft, 0));
    }, 1000);

    return () => clearInterval(currTimeRef.current);
  }, [timer, endTime]);

  const player0Score = getUserScore({ user: players[0] });
  const player1Score = getUserScore({ user: players[1] });

  return (
    <View
      style={[
        styles.container,
        isMobile ? styles.containerMob : styles.containerWeb,
      ]}
    >
      {renderPlayer2({ user: players[0] })}
      <View style={styles.timerMainContainer}>
        {player0Score > player1Score ? (
          <MaterialIcons
            name="arrow-back-ios"
            color={dark.colors.headerUserBorderColor}
            size={10}
          />
        ) : (
          <View styles={styles.emptyIcon} />
        )}
        <View style={[styles.timerContainer]}>
          <MaterialIcons
            name="timer"
            color={dark.colors.timerColor}
            size={10}
          />
          <Text style={styles.timerText2}> {formatTime(timer)}</Text>
        </View>
        {player1Score > player0Score ? (
          <MaterialIcons
            name="arrow-forward-ios"
            color={dark.colors.headerOpponentBorderColor}
            size={10}
          />
        ) : (
          <View styles={styles.emptyIcon} />
        )}
      </View>
      {renderPlayer2({ user: players[1] })}
    </View>
  );
};

export default React.memo(CrossMathPuzzleGameHeader);
